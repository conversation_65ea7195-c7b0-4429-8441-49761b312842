<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Worker extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'position',
        'department',
        'hire_date',
        'hourly_rate',
        'status',
        'employee_id'
    ];

    protected $casts = [
        'hire_date' => 'date',
        'hourly_rate' => 'decimal:2',
    ];

    /**
     * Relation avec les heures supplémentaires
     */
    public function overtimes(): HasMany
    {
        return $this->hasMany(Overtime::class);
    }

    /**
     * Nom complet du travailleur
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Total des heures supplémentaires du mois en cours
     */
    public function getCurrentMonthOvertimeHours(): float
    {
        return $this->overtimes()
            ->whereMonth('date', now()->month)
            ->whereYear('date', now()->year)
            ->sum('hours');
    }

    /**
     * Total des heures supplémentaires payées du mois en cours
     */
    public function getCurrentMonthOvertimePay(): float
    {
        return $this->overtimes()
            ->whereMonth('date', now()->month)
            ->whereYear('date', now()->year)
            ->sum('total_pay');
    }
}
