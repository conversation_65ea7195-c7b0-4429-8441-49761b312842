<?php

namespace App\Livewire;

use App\Models\Worker;
use App\Models\Overtime;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class Dashboard extends Component
{
    public $totalWorkers = 0;
    public $activeWorkers = 0;
    public $totalOvertimeHours = 0;
    public $totalOvertimePay = 0;
    public $recentOvertimes = [];
    public $departmentStats = [];
    public $monthlyOvertimeData = [];

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        try {
            // Statistiques des travailleurs
            $this->totalWorkers = Worker::count();
            $this->activeWorkers = Worker::where('status', 'active')->count();

            // Statistiques des heures supplémentaires du mois en cours
            $currentMonth = now()->month;
            $currentYear = now()->year;

            $this->totalOvertimeHours = Overtime::whereMonth('date', $currentMonth)
                ->whereYear('date', $currentYear)
                ->sum('hours');

            $this->totalOvertimePay = Overtime::whereMonth('date', $currentMonth)
                ->whereYear('date', $currentYear)
                ->where('status', 'approved')
                ->sum('total_pay');

            // Heures supplémentaires récentes
            $this->recentOvertimes = Overtime::with('worker')
                ->latest()
                ->take(5)
                ->get()
                ->toArray();

            // Statistiques par département
            $this->departmentStats = Worker::select('department')
                ->selectRaw('COUNT(*) as worker_count')
                ->selectRaw('AVG(hourly_rate) as avg_rate')
                ->groupBy('department')
                ->get()
                ->toArray();

            // Données mensuelles pour le graphique
            $this->monthlyOvertimeData = $this->getMonthlyOvertimeData();

        } catch (\Exception $e) {
            // En cas d'erreur de base de données, utiliser des données simulées
            $this->loadSimulatedData();
        }
    }

    private function loadSimulatedData()
    {
        $this->totalWorkers = 25;
        $this->activeWorkers = 23;
        $this->totalOvertimeHours = 45.5;
        $this->totalOvertimePay = 1250.75;

        $this->recentOvertimes = [
            [
                'id' => 1,
                'date' => '2024-05-29',
                'hours' => 3.0,
                'total_pay' => 112.50,
                'status' => 'approved',
                'worker' => ['first_name' => 'Jean', 'last_name' => 'Dupont']
            ],
            [
                'id' => 2,
                'date' => '2024-05-28',
                'hours' => 2.5,
                'total_pay' => 84.38,
                'status' => 'pending',
                'worker' => ['first_name' => 'Marie', 'last_name' => 'Martin']
            ],
            [
                'id' => 3,
                'date' => '2024-05-27',
                'hours' => 4.0,
                'total_pay' => 180.00,
                'status' => 'approved',
                'worker' => ['first_name' => 'Pierre', 'last_name' => 'Durand']
            ]
        ];

        $this->departmentStats = [
            ['department' => 'IT', 'worker_count' => 8, 'avg_rate' => 25.50],
            ['department' => 'Marketing', 'worker_count' => 5, 'avg_rate' => 22.00],
            ['department' => 'RH', 'worker_count' => 4, 'avg_rate' => 28.00],
            ['department' => 'Finance', 'worker_count' => 6, 'avg_rate' => 24.75],
            ['department' => 'Production', 'worker_count' => 2, 'avg_rate' => 20.00]
        ];

        $this->monthlyOvertimeData = [
            ['month' => 'Jan', 'hours' => 32.5, 'pay' => 875.25],
            ['month' => 'Fév', 'hours' => 28.0, 'pay' => 756.00],
            ['month' => 'Mar', 'hours' => 41.5, 'pay' => 1120.50],
            ['month' => 'Avr', 'hours' => 38.0, 'pay' => 1026.00],
            ['month' => 'Mai', 'hours' => 45.5, 'pay' => 1250.75]
        ];
    }

    private function getMonthlyOvertimeData()
    {
        $data = [];
        for ($i = 4; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->format('M');
            
            $hours = Overtime::whereMonth('date', $date->month)
                ->whereYear('date', $date->year)
                ->sum('hours');
                
            $pay = Overtime::whereMonth('date', $date->month)
                ->whereYear('date', $date->year)
                ->where('status', 'approved')
                ->sum('total_pay');
                
            $data[] = [
                'month' => $month,
                'hours' => $hours,
                'pay' => $pay
            ];
        }
        
        return $data;
    }

    public function refresh()
    {
        $this->loadDashboardData();
        $this->dispatch('dashboard-refreshed');
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
