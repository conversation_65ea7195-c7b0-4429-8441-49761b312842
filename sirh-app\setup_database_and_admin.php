<?php

/**
 * Script pour créer la base de données et l'utilisateur admin
 * Usage: php setup_database_and_admin.php
 */

echo "=== CONFIGURATION DE LA BASE DE DONNÉES ET ADMIN ===\n\n";

try {
    // Configuration de la base de données
    $host = '127.0.0.1';
    $port = '3306';
    $username = 'root';
    $password = '';
    $database = 'sirh';
    
    echo "1. Connexion à MySQL...\n";
    
    // Connexion à MySQL sans spécifier de base de données
    $pdo = new PDO("mysql:host={$host};port={$port}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion à MySQL réussie !\n";
    
    echo "2. Création de la base de données '{$database}'...\n";
    
    // Créer la base de données si elle n'existe pas
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    echo "✅ Base de données '{$database}' créée ou existe déjà !\n";
    
    echo "3. Sélection de la base de données...\n";
    
    // Se connecter à la base de données spécifique
    $pdo->exec("USE `{$database}`");
    
    echo "✅ Base de données sélectionnée !\n";
    
    echo "4. Création de la table users...\n";
    
    // Créer la table users
    $createUsersTable = "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `email_verified_at` timestamp NULL DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `remember_token` varchar(100) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `users_email_unique` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createUsersTable);
    
    echo "✅ Table users créée ou existe déjà !\n";
    
    echo "5. Création de l'utilisateur admin...\n";
    
    // Paramètres admin
    $adminEmail = '<EMAIL>';
    $adminPassword = 'admin123';
    $adminName = 'Administrateur SIRH';
    
    // Hacher le mot de passe (simulation du Hash::make de Laravel)
    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    // Vérifier si l'admin existe déjà
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "Admin existant trouvé, mise à jour du mot de passe...\n";
        
        // Mettre à jour le mot de passe
        $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE email = ?");
        $stmt->execute([$hashedPassword, $adminEmail]);
        
        echo "✅ Mot de passe admin mis à jour !\n";
    } else {
        echo "Création d'un nouvel utilisateur admin...\n";
        
        // Créer un nouvel admin
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at) 
            VALUES (?, ?, ?, NOW(), NOW(), NOW())
        ");
        $stmt->execute([$adminName, $adminEmail, $hashedPassword]);
        
        echo "✅ Nouvel utilisateur admin créé !\n";
    }
    
    // Récupérer les informations de l'admin
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$adminEmail]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\n=== INFORMATIONS DE CONNEXION ===\n";
    echo "Email: {$admin['email']}\n";
    echo "Mot de passe: {$adminPassword}\n";
    echo "Nom: {$admin['name']}\n";
    echo "ID: {$admin['id']}\n";
    echo "Créé le: {$admin['created_at']}\n";
    echo "Mis à jour le: {$admin['updated_at']}\n";
    
    // Vérifier que le mot de passe fonctionne
    if (password_verify($adminPassword, $admin['password'])) {
        echo "\n✅ Vérification: Le mot de passe est correct !\n";
    } else {
        echo "\n❌ Erreur: Le mot de passe ne correspond pas !\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de base de données: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Configuration terminée avec succès !\n";
echo "Vous pouvez maintenant vous connecter avec:\n";
echo "Email: <EMAIL>\n";
echo "Mot de passe: admin123\n";
