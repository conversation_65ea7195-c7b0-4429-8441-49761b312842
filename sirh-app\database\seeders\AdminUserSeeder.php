<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Supprimer l'utilisateur admin existant s'il existe
        User::where('email', '<EMAIL>')->delete();

        // Créer l'utilisateur administrateur
        User::create([
            'name' => 'Administrateur SIRH',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'email_verified_at' => now(),
        ]);

        echo "Utilisateur administrateur créé avec succès !\n";
        echo "Email: <EMAIL>\n";
        echo "Mot de passe: admin123\n";
    }
}
