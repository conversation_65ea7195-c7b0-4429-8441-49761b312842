<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class ResetAdminPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:reset-password {email?} {password?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset admin password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? $this->ask('Email de l\'administrateur', '<EMAIL>');
        $password = $this->argument('password') ?? $this->secret('Nouveau mot de passe (laissez vide pour "admin123")') ?? 'admin123';

        // Chercher l'utilisateur par email
        $user = User::where('email', $email)->first();

        if (!$user) {
            // Créer un nouvel utilisateur admin s'il n'existe pas
            $user = User::create([
                'name' => 'Administrateur',
                'email' => $email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
            ]);
            
            $this->info("Nouvel utilisateur administrateur créé avec succès !");
        } else {
            // Mettre à jour le mot de passe existant
            $user->update([
                'password' => Hash::make($password),
            ]);
            
            $this->info("Mot de passe administrateur mis à jour avec succès !");
        }

        $this->table(
            ['Champ', 'Valeur'],
            [
                ['Email', $user->email],
                ['Nom', $user->name],
                ['Mot de passe', $password],
                ['Créé le', $user->created_at->format('d/m/Y H:i:s')],
            ]
        );

        return Command::SUCCESS;
    }
}
