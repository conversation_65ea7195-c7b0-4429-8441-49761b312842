<?php

use Illuminate\Support\Facades\Route;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Route temporaire pour réinitialiser le mot de passe admin
// ATTENTION: Supprimez cette route en production !
Route::get('/reset-admin-password', function () {
    $email = '<EMAIL>';
    $password = 'admin123';

    $user = User::where('email', $email)->first();

    if ($user) {
        $user->update([
            'password' => Hash::make($password),
        ]);
        $message = 'Mot de passe administrateur mis à jour avec succès !';
    } else {
        $user = User::create([
            'name' => 'Administrateur SIRH',
            'email' => $email,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
        ]);
        $message = 'Nouvel utilisateur administrateur créé avec succès !';
    }

    return response()->json([
        'success' => true,
        'message' => $message,
        'credentials' => [
            'email' => $email,
            'password' => $password,
            'name' => $user->name,
        ]
    ]);
})->name('reset.admin.password');
